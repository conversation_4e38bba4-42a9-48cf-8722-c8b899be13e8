import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMenuSectionLanguageFields1752812540553 implements MigrationInterface {
  name = 'AddMenuSectionLanguageFields1752812540553';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "published_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "description" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "description_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "description_vi" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "description_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "description_en"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "description"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "published_name_en"`);
  }
}
