import { Language } from '@/common/enums/language.enum';
import { LocalizationHelper } from '@/common/helpers/localization.helper';

import { MenuItem } from './entities/menu-item.entity';

export const localizeMenuItem = (menuItem: MenuItem, userLanguage: Language) => {
  LocalizationHelper.localizeEntity(menuItem, userLanguage);

  if (menuItem.mappingMenuSections) {
    for (const { menuSection } of menuItem.mappingMenuSections) {
      LocalizationHelper.localizeEntity(menuSection, userLanguage);
    }
  }

  if (menuItem.mappingMenuItemOptionGroups) {
    for (const { menuItemOptionGroup } of menuItem.mappingMenuItemOptionGroups) {
      LocalizationHelper.localizeEntity(menuItemOptionGroup, userLanguage);
      if (menuItemOptionGroup.mappingMenuItemOptions) {
        for (const { menuItemOption } of menuItemOptionGroup.mappingMenuItemOptions) {
          LocalizationHelper.localizeEntity(menuItemOption, userLanguage);
        }
      }
    }
  }
};
