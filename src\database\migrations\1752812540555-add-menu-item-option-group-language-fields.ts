import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMenuItemOptionGroupLanguageFields1752812540555 implements MigrationInterface {
  name = 'AddMenuItemOptionGroupLanguageFields1752812540555';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "published_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "description" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "description_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "description_vi" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "description_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "description_en"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "description"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "published_name_en"`);
  }
}
