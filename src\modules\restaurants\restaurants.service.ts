import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { generateCode } from '@/helpers/string';
import { getCurrentTimeByTimeAndDay } from '@/helpers/time';
import { BrandsService } from '@/modules/brands/brands.service';
import { MenuItemType } from '@/modules/menu-items/menu-items.constants';
import { MerchantStaffService } from '@/modules/merchant-staff/merchant-staff.service';
import { RestaurantTagsService } from '@/modules/restaurant-tags/restaurant-tags.service';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MenuItemsService } from '../menu-items/menu-items.service';
import { Menu } from '../menus/entities/menu.entity';
import { MerchantStaff } from '../merchant-staff/entities/merchant-staff.entity';
import { UserAddressesService } from '../user-addresses/user-addresses.service';
import { CreateRestaurantDto, ScheduleItem } from './dtos/create-restaurant.dto';
import { FilterRestaurantDto } from './dtos/filter-restaurant.dto';
import { ListRestaurantDto } from './dtos/list-restaurant.dto';
import { UpdateRestaurantDto } from './dtos/update-restaurant.dto';
import { RestaurantAvailableSchedule } from './entities/restaurant-available-schedule.entity';
import { Restaurant } from './entities/restaurant.entity';

@Injectable()
export class RestaurantsService {
  constructor(
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,

    private dataSource: DataSource,
    private brandsService: BrandsService,
    private restaurantTagsService: RestaurantTagsService,
    private merchantStaffService: MerchantStaffService,
    private userAddressesService: UserAddressesService,
    private menuItemsService: MenuItemsService,
  ) {}

  // Custom validate for restaurant name (brandId scope for internalName, global for publishedName)
  async checkNameExists(
    brandId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      const qb = this.restaurantRepository
        .createQueryBuilder('restaurant')
        .where('restaurant.brandId = :brandId', { brandId })
        .andWhere('restaurant.internalName = :internalName', { internalName })
        .andWhere('restaurant.deletedAt IS NULL');
      if (excludeId) {
        qb.andWhere('restaurant.id != :excludeId', { excludeId });
      }
      const count = await qb.getCount();
      if (count > 0) return true;
    }
    if (publishedName) {
      const qb = this.restaurantRepository
        .createQueryBuilder('restaurant')
        .where('restaurant.publishedName = :publishedName', { publishedName })
        .andWhere('restaurant.deletedAt IS NULL');
      if (excludeId) {
        qb.andWhere('restaurant.id != :excludeId', { excludeId });
      }
      const count = await qb.getCount();
      if (count > 0) return true;
    }
    return false;
  }

  // Custom validate unique for restaurant
  async validateUniqueNames(
    brandId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<void> {
    if (internalName) {
      const exists = await this.checkNameExists(brandId, internalName, undefined, excludeId);
      if (exists) throw new BadRequestException('Internal name already exists');
    }
    if (publishedName) {
      const exists = await this.checkNameExists(brandId, undefined, publishedName, excludeId);
      if (exists) throw new BadRequestException('Published name already exists');
    }
  }

  async create(createRestaurantDto: CreateRestaurantDto, ownerId: string | null): Promise<Restaurant> {
    const {
      tagIds,
      brandId,
      isActive,
      scheduleActiveAt,
      availableSchedule,
      latitude,
      longitude,
      activeMenuId,
      ...restaurantData
    } = createRestaurantDto;

    // Custom validate unique names
    await this.validateUniqueNames(
      brandId,
      createRestaurantDto.internalName,
      createRestaurantDto.publishedName,
      undefined,
    );

    // For merchant users, verify they have access to the brand they're trying to create a restaurant for
    await this.brandsService.verifyAccessBrand(brandId, ownerId);
    let merchantStaff: MerchantStaff = new MerchantStaff();
    let savedRestaurantId: string = '';
    await this.dataSource.transaction(async (manager) => {
      // Create restaurant entity
      const restaurant = manager.create(Restaurant, {
        ...restaurantData,
        brandId,
        code: generateCode(3),
      });

      // Set location geometry if coordinates are provided
      restaurant.latitude = latitude;
      restaurant.longitude = longitude;
      restaurant.location = {
        type: 'Point',
        coordinates: [longitude, latitude],
      };

      if (isActive) {
        restaurant.activeAt = new Date();
        restaurant.scheduleActiveAt = null;
      } else if (scheduleActiveAt) {
        restaurant.scheduleActiveAt = new Date(scheduleActiveAt);
      }

      // Handle tag relationship
      await this.handleTagRelationship(restaurant, tagIds);

      // Save the restaurant with relationships using transaction manager
      const savedRestaurant = await manager.save(Restaurant, restaurant);

      // Handle available schedule if provided
      await this.handleAvailableSchedule(savedRestaurant, availableSchedule, manager);

      // Handle active menu if provided
      if (activeMenuId) {
        await this.handleActiveMenu(savedRestaurant.id, activeMenuId, manager);
      }

      // Create merchant staff for this restaurant within the same transaction
      merchantStaff = await this.merchantStaffService.create(savedRestaurant.id, manager);
      savedRestaurantId = savedRestaurant.id;
    });

    // Return restaurant with merchant staff info
    const restaurantWithDetails = await this.findOne(savedRestaurantId, ownerId);

    restaurantWithDetails['merchantStaff'] = {
      id: merchantStaff.id,
      username: merchantStaff.username,
      banned: merchantStaff.banned,
    };

    return restaurantWithDetails;
  }

  async findAll(listRestaurantDto: ListRestaurantDto, ownerId: string | null) {
    const { search, openNow, minStarRated, brandId, restaurantTagIds, rating, page, limit } = listRestaurantDto;

    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    // Add relations to get access to brand and merchant account data
    queryBuilder.leftJoinAndSelect('restaurant.brand', 'brand');
    queryBuilder.leftJoinAndSelect('restaurant.tags', 'tags');
    queryBuilder.leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule');

    // Search filter: search by published name and restaurant tags (admin version can also search internalName)
    if (search) {
      const similarityThreshold = 0.2;
      queryBuilder.andWhere(
        '(restaurant.internalName ILIKE :searchPattern OR restaurant.publishedName ILIKE :searchPattern OR similarity(restaurant.publishedName, :searchText) > :threshold OR EXISTS (SELECT 1 FROM mapping_restaurant_tags_restaurants mrt INNER JOIN restaurant_tags rt ON mrt.restaurant_tag_id = rt.id WHERE mrt.restaurant_id = restaurant.id AND (rt.name ILIKE :searchPattern OR similarity(rt.name, :searchText) > :threshold)))',
        {
          searchPattern: `%${search}%`,
          searchText: search,
          threshold: similarityThreshold,
        },
      );
    }

    if (openNow === true) {
      const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();
      queryBuilder.andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from(RestaurantAvailableSchedule, 'ras')
          .where('ras.restaurant_id = restaurant.id')
          .andWhere('ras.day = :currentDayOfWeek', { currentDayOfWeek })
          .andWhere('(ras.is_all_day = true OR (ras.start <= :currentTime AND ras.end >= :currentTime))', {
            currentTime,
          });
        return `EXISTS ${subQuery.getQuery()}`;
      });
    }

    if (minStarRated !== undefined) {
      queryBuilder.andWhere('restaurant.starRated >= :minStarRated', { minStarRated });
    }

    // For merchant users, only show restaurants associated with brands they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    // Apply brandId filter if provided
    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    if (restaurantTagIds?.length) {
      queryBuilder.andWhere('tag.id IN (:...restaurantTagIds)', { restaurantTagIds }); // Filter by tag ID
    }

    if (rating === true) {
      queryBuilder.orderBy('restaurant.starRated', 'DESC');
    } else {
      queryBuilder.orderBy('restaurant.updatedAt', 'DESC');
    }

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string, ownerId: string | null): Promise<Restaurant> {
    // Create query builder to handle user-specific conditions
    const queryBuilder = this.restaurantRepository
      .createQueryBuilder('restaurant')
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .leftJoinAndSelect('restaurant.menus', 'menus')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule')
      .where('restaurant.id = :id', { id });

    // For merchant users, only allow access to restaurants associated with brands they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant with ID ${id} not found or you don't have access to it`);
    }

    return restaurant;
  }

  async update(id: string, updateRestaurantDto: UpdateRestaurantDto, ownerId: string | null): Promise<Restaurant> {
    const restaurant = await this.findOne(id, ownerId);
    const {
      tagIds,
      isActive,
      scheduleActiveAt,
      availableSchedule,
      latitude,
      longitude,
      activeMenuId,
      ...restaurantData
    } = updateRestaurantDto;

    // Custom validate unique names
    await this.validateUniqueNames(
      restaurant.brandId,
      updateRestaurantDto.internalName,
      updateRestaurantDto.publishedName,
      id,
    );

    return await this.dataSource.transaction(async (manager) => {
      // If we get here, the user has permission to update the restaurant
      Object.assign(restaurant, restaurantData);

      // Update location geometry if coordinates are provided
      if (latitude !== undefined && longitude !== undefined) {
        restaurant.latitude = latitude;
        restaurant.longitude = longitude;
        restaurant.location = {
          type: 'Point',
          coordinates: [longitude, latitude],
        };
      }

      // Handle available schedule if provided
      await this.handleAvailableSchedule(restaurant, availableSchedule, manager);

      // Handle tag relationship
      await this.handleTagRelationship(restaurant, tagIds);

      const restaurantWasActive = restaurant.activeAt !== null;
      // Handle isActive and scheduleActiveAt logic
      if (isActive !== undefined && restaurantWasActive !== isActive) {
        restaurant.activeAt = isActive ? new Date() : null;
      }

      if (!restaurant.activeAt && scheduleActiveAt !== undefined) {
        restaurant.scheduleActiveAt = scheduleActiveAt ? new Date(scheduleActiveAt) : null;
      }

      // Handle active menu if provided
      if (activeMenuId) {
        await this.handleActiveMenu(restaurant.id, activeMenuId, manager);
      }

      return manager.save(Restaurant, restaurant);
    });
  }

  async activate(id: string, ownerId: string | null): Promise<Restaurant> {
    const restaurant = await this.findOne(id, ownerId);

    // If we get here, the user has permission to activate the restaurant
    restaurant.activeAt = new Date();

    return this.restaurantRepository.save(restaurant);
  }

  async deactivate(id: string, ownerId: string | null): Promise<Restaurant> {
    const restaurant = await this.findOne(id, ownerId);

    // If we get here, the user has permission to deactivate the restaurant
    restaurant.activeAt = null;

    return this.restaurantRepository.save(restaurant);
  }

  // user API
  async userFindAll(filterRestaurantDto: FilterRestaurantDto, userId: string): Promise<Pagination<Restaurant>> {
    const {
      search,
      openNow,
      minStarRated,
      brandId,
      restaurantTagIds,
      page,
      limit,
      sortBy,
      sort,
      addressId,
      isFavourite,
    } = filterRestaurantDto;

    const userAddress = await this.getUserAddress(userId, addressId);

    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    // Add relations to get access to brand and merchant account data
    queryBuilder
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule')
      .leftJoin('brand.merchantAccount', 'merchantAccount')
      .leftJoin('restaurant.favourites', 'favourites', 'favourites.user_id = :userId', { userId })
      .addSelect(
        `ST_Distance(restaurant.location, ST_GeomFromText('POINT(${userAddress.longitude} ${userAddress.latitude})', 4326)::geography)`,
        'distance',
      )
      .addSelect('CASE WHEN favourites.id IS NOT NULL THEN true ELSE false END', 'isFavourite')
      .andWhere('brand.activeAt IS NOT NULL')
      .andWhere('merchantAccount.activeAt IS NOT NULL');

    // Apply geofencing filter if lat/lng provided
    queryBuilder
      .innerJoin('restaurant.geofencing', 'geofencing')
      .andWhere('ST_Contains(geofencing.geometry, ST_GeomFromText(:point, 4326))', {
        point: `POINT(${userAddress.longitude} ${userAddress.latitude})`,
      });

    if (isFavourite) {
      queryBuilder.andWhere('favourites.id IS NOT NULL');
    }

    // Search filter: search by published name and restaurant tags
    if (search) {
      const similarityThreshold = 0.2;
      queryBuilder.andWhere(
        '(restaurant.publishedName ILIKE :searchPattern OR similarity(restaurant.publishedName, :searchText) > :threshold OR EXISTS (SELECT 1 FROM mapping_restaurant_tags_restaurants mrt INNER JOIN restaurant_tags rt ON mrt.restaurant_tag_id = rt.id WHERE mrt.restaurant_id = restaurant.id AND (rt.name ILIKE :searchPattern OR similarity(rt.name, :searchText) > :threshold)))',
        {
          searchPattern: `%${search}%`,
          searchText: search,
          threshold: similarityThreshold,
        },
      );
    }

    // Open now filter: check if restaurant is currently open based on schedule
    if (openNow === true) {
      const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();

      queryBuilder.andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('ras.restaurantId')
          .from(RestaurantAvailableSchedule, 'ras')
          .where('ras.restaurantId = restaurant.id')
          .andWhere('ras.day = :currentDayOfWeek', { currentDayOfWeek })
          .andWhere('(ras.isAllDay = true OR (ras.start <= :currentTime AND ras.end >= :currentTime))', {
            currentTime,
          });
        return `restaurant.id IN ${subQuery.getQuery()}`;
      });
      queryBuilder.andWhere('restaurant.activeAt IS NOT NULL');
    }

    if (minStarRated !== undefined) {
      queryBuilder.andWhere('restaurant.starRated >= :minStarRated', { minStarRated });
    }

    // Apply brandId filter if provided
    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    if (restaurantTagIds?.length) {
      queryBuilder.andWhere('tags.id IN (:...restaurantTagIds)', { restaurantTagIds }); // Filter by tag ID
    }

    // Handle sorting - distance is a computed field, others are restaurant fields
    if (sortBy === 'distance') {
      queryBuilder.orderBy('distance', sort);
    } else {
      queryBuilder.orderBy(`restaurant.${sortBy}`, sort);
    }

    const totalCount = await queryBuilder.getCount();
    const { entities, raw } = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getRawAndEntities();

    // Map the raw data to entities to include addSelect fields
    const itemsWithFavourites = entities.map((entity) => {
      const rawItem = raw.find((r) => r.restaurant_id === entity.id);
      entity.distance = rawItem.distance;
      entity.isFavourite = rawItem.isFavourite === 'true' || rawItem.isFavourite === true;
      return entity;
    });

    return {
      items: itemsWithFavourites,
      meta: {
        totalItems: totalCount,
        itemCount: itemsWithFavourites.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
      },
    };
  }

  async userFindOne(id: string, userId: string, addressId?: string | null): Promise<Restaurant> {
    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');
    const userAddress = await this.getUserAddress(userId, addressId);

    queryBuilder
      .select([
        'restaurant.id',
        'restaurant.internalName',
        'restaurant.publishedName',
        'restaurant.avatarImg',
        'restaurant.backgroundImg',
        'restaurant.starRated',
        'restaurant.totalReviews',
        'restaurant.activeAt',
        'restaurant.scheduleActiveAt',
      ])
      .where('restaurant.id = :id', { id })
      .leftJoin('restaurant.brand', 'brand')
      .leftJoin('brand.merchantAccount', 'merchantAccount')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule2')
      .addSelect(['brand.id', 'brand.name'])
      .andWhere('brand.activeAt IS NOT NULL')
      .andWhere('merchantAccount.activeAt IS NOT NULL')
      .leftJoin('restaurant.favourites', 'favourites', 'favourites.user_id = :userId', { userId })
      .leftJoin(
        'restaurant.menus',
        'menus',
        'menus.activeAt IS NOT NULL AND menus.id = (SELECT m.id FROM menus m WHERE m.restaurant_id = restaurant.id AND m.active_at IS NOT NULL ORDER BY m.active_at DESC LIMIT 1)',
      )
      .addSelect(['menus.id', 'menus.name', 'menus.activeAt'])
      .leftJoin('menus.mappingMenuSections', 'mappingMenuSections')
      .addSelect(['mappingMenuSections.position'])
      .leftJoin('mappingMenuSections.menuSection', 'menuSection')
      .addSelect([
        'menuSection.id',
        'menuSection.publishedName',
        'menuSection.viewType',
        'menuSection.activeAt',
        'menuSection.scheduleActiveAt',
      ])
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenuItems', 'mappingMenuItems')
      .leftJoin('mappingMenuItems.menuItem', 'menuItem', 'menuItem.type = :itemType', { itemType: MenuItemType.ITEM })
      .addSelect([
        'menuItem.id',
        'menuItem.publishedName',
        'menuItem.description',
        'menuItem.basePrice',
        'menuItem.imageUrls',
        'menuItem.activeAt',
        'menuItem.scheduleActiveAt',
      ]);

    queryBuilder
      .addSelect(
        `ST_Distance(restaurant.location, ST_GeomFromText('POINT(${userAddress.longitude} ${userAddress.latitude})', 4326)::geography)`,
        'distance',
      )
      .addSelect('CASE WHEN favourites.id IS NOT NULL THEN true ELSE false END', 'isFavourite');
    // Apply geofencing filter if lat/lng provided
    queryBuilder
      .innerJoin('restaurant.geofencing', 'geofencing')
      .andWhere('ST_Contains(geofencing.geometry, ST_GeomFromText(:point, 4326))', {
        point: `POINT(${userAddress.longitude} ${userAddress.latitude})`,
      });

    const { entities, raw } = await queryBuilder.getRawAndEntities();

    const restaurant = entities[0];
    const rawRestaurant = raw[0];

    // Map distance and favourite status from raw data if available
    if (rawRestaurant.distance !== undefined) {
      restaurant.distance = rawRestaurant.distance;
    }
    restaurant.isFavourite = rawRestaurant.isFavourite === 'true' || rawRestaurant.isFavourite === true;

    if (!restaurant) {
      throw new NotFoundException(`Restaurant not found`);
    }

    const popularItems = await this.menuItemsService.getPopularItems(restaurant.id);

    restaurant.popularItems = popularItems;

    // Transform the result to have activeMenu instead of menus array
    if (restaurant.menus && restaurant.menus.length > 0) {
      restaurant.menu = restaurant.menus[0];
    }

    if (restaurant.menus) delete restaurant.menus;

    return restaurant;
  }

  private async getUserAddress(userId: string, addressId?: string | null) {
    if (addressId) {
      return this.userAddressesService.findOneByUserAndId(addressId, userId);
    }

    const defaultAddress = await this.userAddressesService.getDefaultAddress(userId);

    if (!defaultAddress) {
      throw new NotFoundException('User does not have address to find restaurants');
    }

    return defaultAddress;
  }

  async staffFindOne(id: string): Promise<Restaurant> {
    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    queryBuilder
      .where('restaurant.id = :id', { id })
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoin('brand.merchantAccount', 'merchantAccount')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .addSelect(['brand.id', 'brand.name'])
      .andWhere('brand.activeAt IS NOT NULL')
      .andWhere('merchantAccount.activeAt IS NOT NULL')
      .leftJoinAndSelect(
        'restaurant.menus',
        'menus',
        'menus.activeAt IS NOT NULL AND menus.id = (SELECT m.id FROM menus m WHERE m.restaurant_id = restaurant.id AND m.active_at IS NOT NULL ORDER BY m.active_at DESC LIMIT 1)',
      )
      .leftJoinAndSelect('menus.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .innerJoinAndSelect('menuSection.mappingMenuItems', 'mappingMenuItems')
      .innerJoinAndSelect('mappingMenuItems.menuItem', 'menuItem', 'menuItem.type = :itemType', {
        itemType: MenuItemType.ITEM,
      });

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant not found`);
    }

    // Transform the result to have activeMenu instead of menus array
    if (restaurant.menus && restaurant.menus.length > 0) {
      restaurant.menu = restaurant.menus[0];
    }

    if (restaurant.menus) delete restaurant.menus;

    return restaurant;
  }

  async softDelete(id: string, ownerId: string | null): Promise<Restaurant> {
    const restaurant = await this.findOne(id, ownerId);
    return await this.restaurantRepository.softRemove(restaurant);
  }

  /**
   * Increment restaurant's total orders sold by 1 using entity manager (for transactions)
   * @param manager The entity manager for transaction
   * @param restaurantId The ID of the restaurant to update
   */
  async incrementRestaurantTotalOrdersSold(manager: EntityManager, restaurantId: string): Promise<void> {
    await manager.increment(Restaurant, { id: restaurantId }, 'totalOrdersSold', 1);
  }

  async getSuggestions(searchText: string): Promise<string[]> {
    const similarityThreshold = 0.2; // Adjust threshold as needed (0.0 to 1.0)
    // Query for restaurant tags and restaurant published names concurrently
    const [tagSuggestions, restaurantResults] = await Promise.all([
      this.restaurantTagsService.getSuggestionsWithFuzzyMatching(searchText, 3),
      this.restaurantRepository
        .createQueryBuilder('restaurant')
        .select(['restaurant.publishedName'])
        .addSelect('similarity(restaurant.publishedName, :searchText)', 'similarity_score')
        .innerJoin('restaurant.brand', 'brand')
        .innerJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('brand.activeAt IS NOT NULL')
        .andWhere('merchantAccount.activeAt IS NOT NULL')
        .andWhere(
          '(restaurant.publishedName ILIKE :searchPattern OR similarity(restaurant.publishedName, :searchText) > :threshold)',
          {
            searchPattern: `%${searchText}%`,
            searchText,
            threshold: similarityThreshold,
          },
        )
        .orderBy('similarity_score', 'DESC')
        .addOrderBy('restaurant.publishedName', 'ASC')
        .limit(10)
        .getMany(),
    ]);

    const suggestions: string[] = [
      ...tagSuggestions,
      ...restaurantResults.map((restaurant) => restaurant.publishedName),
    ];
    // Remove duplicates and return max 10 items
    return [...new Set(suggestions)].slice(0, 10);
  }

  async handleTagRelationship(restaurant: Restaurant, tagIds?: string[]) {
    // If tagIds is undefined, don't update menuItems
    if (tagIds === undefined) {
      return;
    }

    // If tagIds is an empty array, clear all tags
    if (tagIds.length === 0) {
      restaurant.tags = [];
      return;
    }

    // Find tags that match both the provided IDs and the brand ID
    const tags = await this.restaurantTagsService.getListById(tagIds);

    // Check if all tag IDs exist and belong to the brand
    if (tags.length !== tagIds.length) {
      const foundIds = tags.map((tag) => tag.id);
      const missingIds = tagIds.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following tag IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Set the menu items relation
    restaurant.tags = tags;
  }

  /**
   * Helper method to handle available schedule relationships for restaurants
   * @param restaurant The restaurant entity to update
   * @param availableSchedule Array of schedule items to link, or undefined to skip updating
   * @param entityManager The entity manager for transaction
   */
  private async handleAvailableSchedule(
    restaurant: Restaurant,
    availableSchedule: ScheduleItem[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    delete restaurant.availableSchedule;
    // If availableSchedule is undefined, don't update schedules
    if (availableSchedule === undefined) {
      return;
    }

    // Clear existing schedule relationships
    if (restaurant.id) {
      await entityManager.delete(RestaurantAvailableSchedule, { restaurantId: restaurant.id });
    }

    // If availableSchedule is an empty array, we're done (all schedules cleared)
    if (availableSchedule.length === 0) {
      return;
    }

    // Create new schedule relationships
    if (restaurant.id) {
      const scheduleData = availableSchedule.map((schedule) => {
        // If isAllDay is true, start and end should be null
        const start = schedule.start;
        const end = schedule.end;

        return entityManager.create(RestaurantAvailableSchedule, {
          restaurantId: restaurant.id,
          day: schedule.day,
          start,
          end,
          isAllDay: schedule.isAllDay || false,
        });
      });
      await entityManager.save(scheduleData);
    }
  }

  /**
   * Helper method to handle active menu for restaurants
   * @param restaurantId The restaurant ID
   * @param activeMenuId The menu ID to activate
   * @param entityManager The entity manager for transaction
   */
  private async handleActiveMenu(
    restaurantId: string,
    activeMenuId: string,
    entityManager: EntityManager,
  ): Promise<void> {
    // Then activate the specified menu
    const menu = await entityManager.findOne(Menu, { where: { id: activeMenuId, restaurantId } });
    if (!menu) {
      throw new NotFoundException(`Menu with ID ${activeMenuId} not found for restaurant ${restaurantId}`);
    }

    // First, deactivate all menus for this restaurant
    await entityManager.update(Menu, { restaurantId }, { activeAt: null });
    await entityManager.update(Menu, { id: activeMenuId }, { activeAt: new Date() });
  }

  async findRestaurantWithBrandAndMerchantAccount(restaurantId: string): Promise<Restaurant | null> {
    return this.restaurantRepository.findOne({
      where: { id: restaurantId },
      relations: ['brand', 'brand.merchantAccount'],
    });
  }
}
