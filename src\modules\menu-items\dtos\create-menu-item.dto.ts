import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { GroupItemWithPriceDto } from '@/common/dtos/group-item-with-price.dto';
import { PositionItemDto } from '@/common/dtos/position-item.dto';
import { SectionItemWithPriceDto } from '@/common/dtos/section-item-with-price.dto';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

import { MenuItemType } from '../menu-items.constants';

export class CreateMenuItemDto {
  @ApiProperty({ description: 'Internal name of the menu item' })
  @IsString()
  internalName: string;

  @ApiProperty({ description: 'Published name of the menu item' })
  @IsNotEmpty()
  @IsString()
  publishedName: string;

  @ApiProperty({ description: 'Description of the menu item', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Base price of the menu item' })
  @IsInt()
  @Min(0)
  @Type(() => Number)
  basePrice: number;

  @ApiProperty({ description: 'Type of the menu item', enum: MenuItemType, default: MenuItemType.ITEM })
  @IsEnum(MenuItemType)
  type: MenuItemType;

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'Is alcohol', required: false, example: false })
  @IsOptional()
  @IsBoolean()
  isAlcohol?: boolean;

  @ApiProperty({ description: 'ID of the restaurant' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({ description: 'Array of ingredient IDs', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  ingredientIds?: string[];

  @ApiProperty({
    description: 'Array of menu item option group IDs with positions',
    required: false,
    type: [GroupItemWithPriceDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GroupItemWithPriceDto)
  menuItemOptionGroupsOfOption?: GroupItemWithPriceDto[];

  @ApiProperty({
    description: 'Array of menu item option group IDs with positions',
    required: false,
    type: [PositionItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemDto)
  menuItemOptionGroupsOfItem?: PositionItemDto[];

  @ApiProperty({
    description: 'Array of menu section IDs',
    required: false,
    type: [SectionItemWithPriceDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionItemWithPriceDto)
  menuSectionIds?: SectionItemWithPriceDto[];

  @ApiProperty({
    description: 'Array of menu item image URLs (required for menu items)',
    type: [String],
    required: false,
  })
  @ValidateIf((o) => o.type === MenuItemType.ITEM)
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsValidS3Url(FolderType.MENU_ITEMS)
  imageUrls?: string[];
}
