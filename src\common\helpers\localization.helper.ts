import { Language } from '@/common/enums/language.enum';

export interface LocalizableEntity {
  publishedName?: string;
  publishedNameVi?: string | null;
  description?: string | null;
  descriptionVi?: string | null;
}

export class LocalizationHelper {
  static getLocalizedPublishedName(entity: LocalizableEntity, userLanguage: Language): string | null | undefined {
    if (userLanguage === Language.VI) {
      return entity.publishedNameVi;
    }
    return entity.publishedName; // Default to English
  }

  static getLocalizedDescription(entity: LocalizableEntity, userLanguage: Language): string | null | undefined {
    if (userLanguage === Language.VI && entity.descriptionVi) {
      return entity.descriptionVi;
    }
    return entity.description; // Default to English
  }

  static localizeEntity<T extends LocalizableEntity>(entity: T, userLanguage: Language) {
    if (!entity) return;

    if ('publishedName' in entity) {
      // Override publishedName with localized version
      entity.publishedName = this.getLocalizedPublishedName(entity, userLanguage) || entity.publishedName;
    }

    if ('description' in entity) {
      // Override description with entity version
      entity.description = this.getLocalizedDescription(entity, userLanguage) || entity.description;
    }
  }

  static localizeEntities<T extends LocalizableEntity>(entities: T[], userLanguage: Language) {
    return entities.map((entity) => this.localizeEntity(entity, userLanguage));
  }
}
