import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserLanguage1752812540552 implements MigrationInterface {
  name = 'AddUserLanguage1752812540552';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."users_language_enum" AS ENUM('en', 'vi')`);
    await queryRunner.query(`ALTER TABLE "users" ADD "language" "public"."users_language_enum" NOT NULL DEFAULT 'en'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "language"`);
    await queryRunner.query(`DROP TYPE "public"."users_language_enum"`);
  }
}
