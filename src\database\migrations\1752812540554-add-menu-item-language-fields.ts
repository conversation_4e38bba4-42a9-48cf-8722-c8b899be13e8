import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMenuItemLanguageFields1752812540554 implements MigrationInterface {
  name = 'AddMenuItemLanguageFields1752812540554';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "published_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "description_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "description_vi" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "description_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "description_en"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "published_name_en"`);
  }
}
