import { Request, Response } from 'express';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { CreateTemporaryUserAddressDto } from '@/modules/user-addresses/dto/create-user-address.dto';
import { UserAddressesService } from '@/modules/user-addresses/user-addresses.service';
import { UsersService } from '@/modules/users/users.service';
import { Public } from '@auth/decorators/public.decorator';
import { RefreshTokenDto } from '@auth/dtos/refresh-token.dto';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Post, Req, Res } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { SkipProfileCheck } from '../../decorators/skip-profile-check.decorator';
import { UserJwtInfo } from '../../types/jwt-payload.type';
import { AuthUserService } from './auth-user.service';
import {
  SendEmailOtpDto,
  SendPhoneOtpDto,
  UpdateUserNameDto,
  VerifyEmailOtpDto,
  VerifyPhoneOtpDto,
} from './dtos/onboarding.dto';
import {
  RequestUpdateEmailDto,
  RequestUpdatePhoneDto,
  VerifyUpdateEmailDto,
  VerifyUpdatePhoneDto,
} from './dtos/update-profile.dto';
import { OnboardingService } from './onboarding.service';
import { ProfileUpdateService } from './profile-update.service';

@ApiTags('(Auth) User')
@Controller('auth/user')
export class AuthUserController {
  constructor(
    private readonly authUserService: AuthUserService,
    private readonly usersService: UsersService,
    private readonly onboardingService: OnboardingService,
    private readonly userAddressesService: UserAddressesService,
    private readonly profileUpdateService: ProfileUpdateService,
  ) {}

  @Public()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh user token' })
  async refreshToken(
    @Req() request: Request,
    @Body() refreshTokenDto: RefreshTokenDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    return this.authUserService.refreshToken(request, refreshTokenDto, response);
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'User logout' })
  logout(@Res({ passthrough: true }) response: Response) {
    return this.authUserService.logout(response);
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @SkipProfileCheck()
  @Get('me')
  @ApiOperation({ summary: 'Get current user info' })
  getMe(@User() user: UserJwtInfo) {
    return this.usersService.getMe(user.id);
  }

  // Onboarding endpoints
  @Public()
  @Post('send-phone-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send OTP to phone number' })
  sendPhoneOtp(@Body() dto: SendPhoneOtpDto) {
    return this.onboardingService.sendPhoneOtp(dto);
  }

  @Public()
  @Post('verify-phone-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify phone OTP and get verification token' })
  verifyPhoneOtp(@Body() dto: VerifyPhoneOtpDto) {
    return this.onboardingService.verifyPhoneOtp(dto);
  }

  @Public()
  @Post('send-email-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send OTP to email' })
  async sendEmailOtp(@Body() dto: SendEmailOtpDto) {
    return this.onboardingService.sendEmailOtp(dto);
  }

  @Public()
  @Post('verify-email-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify email OTP and complete login' })
  async verifyEmailOtp(@Body() dto: VerifyEmailOtpDto, @Res({ passthrough: true }) response: Response) {
    return this.onboardingService.verifyEmailOtpAndLogin(dto, response);
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @SkipProfileCheck()
  @Post('update-profile')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update user first name and last name' })
  async updateUserName(@User() user: UserJwtInfo, @Body() dto: UpdateUserNameDto) {
    return this.onboardingService.updateUserName(user.id, dto);
  }

  // Temporary Address Endpoints
  @Roles({ userType: UserType.USER, role: '*' })
  @SkipProfileCheck()
  @Get('temporary')
  async getTemporaryAddress(@User('id') userId: string) {
    return await this.userAddressesService.getTemporaryAddress(userId);
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @SkipProfileCheck()
  @Post('temporary')
  async createTemporaryAddress(
    @User('id') userId: string,
    @Body() createTemporaryAddressDto: CreateTemporaryUserAddressDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const address = await this.userAddressesService.createOrUpdateTemporaryAddress(createTemporaryAddressDto, userId);

    const loginResponse = await this.authUserService.loginByUserId(userId, response);
    return {
      ...loginResponse,
      address,
    };
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @Post('request-update-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request email update' })
  async requestUpdateEmail(@User() user: UserJwtInfo, @Body() dto: RequestUpdateEmailDto) {
    return this.profileUpdateService.requestUpdateEmail(user.id, dto);
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @Post('verify-update-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify email update' })
  async verifyUpdateEmail(@User() user: UserJwtInfo, @Body() dto: VerifyUpdateEmailDto, @Req() request: Request) {
    return this.profileUpdateService.verifyUpdateEmail(user.id, dto, request);
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @Post('request-update-phone')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request phone update' })
  async requestUpdatePhone(@User() user: UserJwtInfo, @Body() dto: RequestUpdatePhoneDto) {
    return this.profileUpdateService.requestUpdatePhone(user.id, dto);
  }

  @Roles({ userType: UserType.USER, role: '*' })
  @Post('verify-update-phone')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify phone update' })
  async verifyUpdatePhone(@User() user: UserJwtInfo, @Body() dto: VerifyUpdatePhoneDto, @Req() request: Request) {
    return this.profileUpdateService.verifyUpdatePhone(user.id, dto, request);
  }

  @Public()
  @Delete()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Delete user account' })
  async deleteUser(@Req() request: Request) {
    // Try to manually extract and verify JWT token
    let token;
    let user: UserJwtInfo | null = null;
    const access_token = request?.cookies?.access_token;
    const authorization = request?.headers?.authorization;
    if (access_token) {
      token = access_token;
    } else if (authorization && authorization.startsWith('Bearer ')) {
      token = authorization.substring(7);
    }

    if (token) {
      // Use the auth service to verify and decode the token
      user = await this.authUserService.verifyAccessToken(token);
    }

    // If no authentication, return 201 and do nothing
    if (!user) {
      return { message: 'Request processed' };
    }

    // If user is authenticated, perform soft delete
    await this.usersService.softDelete(user.id);
    return { message: 'User account deleted successfully' };
  }
}
