import { Language } from '@/common/enums/language.enum';
import { LocalizationHelper } from '@/common/helpers/localization.helper';

import type { Restaurant } from './entities/restaurant.entity';
import type { MenuItemOptionGroup } from '../menu-item-option-groups/entities/menu-item-option-group.entity';
import type { MenuItem } from '../menu-items/entities/menu-item.entity';
import type { MenuSection } from '../menu-sections/entities/menu-section.entity';

export const localizeRestaurant = (restaurant: Restaurant, userLanguage: Language) => {
  LocalizationHelper.localizeEntity(restaurant, userLanguage);

  if (!restaurant.menu?.mappingMenuSections) return;

  for (const { menuSection } of restaurant.menu.mappingMenuSections) {
    localizeMenuSection(menuSection, userLanguage);
  }
};

const localizeMenuSection = (menuSection: MenuSection, userLanguage: Language) => {
  LocalizationHelper.localizeEntity(menuSection, userLanguage);

  if (!menuSection.mappingMenuItems) return;

  for (const { menuItem } of menuSection.mappingMenuItems) {
    localizeMenuItem(menuItem, userLanguage);
  }
};

const localizeMenuItem = (menuItem: MenuItem, userLanguage: Language) => {
  LocalizationHelper.localizeEntity(menuItem, userLanguage);

  if (!menuItem.mappingMenuItemOptionGroups) return;

  for (const { menuItemOptionGroup } of menuItem.mappingMenuItemOptionGroups) {
    localizeMenuItemOptionGroup(menuItemOptionGroup, userLanguage);
  }
};

const localizeMenuItemOptionGroup = (menuItemOptionGroup: MenuItemOptionGroup, userLanguage: Language) => {
  LocalizationHelper.localizeEntity(menuItemOptionGroup, userLanguage);

  if (!menuItemOptionGroup.mappingMenuItemOptions) return;

  for (const { menuItemOption } of menuItemOptionGroup.mappingMenuItemOptions) {
    LocalizationHelper.localizeEntity(menuItemOption, userLanguage);
  }
};
