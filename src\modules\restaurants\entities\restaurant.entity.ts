import { Column, <PERSON>tity, Index, Join<PERSON><PERSON><PERSON>n, <PERSON>in<PERSON><PERSON>, ManyToMany, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { Geofencing } from '@/modules/geofencing/entities/geofencing.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { Menu } from '@/modules/menus/entities/menu.entity';
import { RestaurantTag } from '@/modules/restaurant-tags/entities/restaurant-tag.entity';
import { UserFavouriteRestaurant } from '@/modules/user-favourite-restaurants/entities/user-favourite-restaurant.entity';

import { RestaurantAvailableSchedule } from './restaurant-available-schedule.entity';

@Entity('restaurants')
@Index(['brandId', 'internalName'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['publishedName'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['brandId', 'activeAt']) // Brand filtering with active status
@Index('idx_restaurants_published_name_trigram', { synchronize: false }) // GIN index for fuzzy search, sync=false vì tạo qua migration
export class Restaurant extends BaseEntity {
  @Column({ name: 'internal_name', type: 'varchar' })
  internalName: string;

  @Column({ name: 'published_name', type: 'varchar' })
  publishedName: string;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'code', type: 'varchar' })
  code: string;

  @Column({ name: 'avatar_img', type: 'varchar' })
  avatarImg: string;

  @Column({ name: 'background_img', type: 'varchar' })
  backgroundImg: string;

  @Index()
  @Column({ name: 'price_range', nullable: true, type: 'varchar' })
  priceRange?: string | null;

  @Index()
  @Column({
    name: 'star_rated',
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  starRated: number;

  @Index()
  @Column({ name: 'total_reviews', type: 'integer', default: 0 })
  totalReviews: number;

  @Index()
  @Column({ name: 'total_orders_sold', type: 'integer', default: 0 })
  totalOrdersSold: number;

  @Column({ name: 'brand_id', type: 'uuid' })
  brandId: string;

  @Index()
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @OneToMany(() => RestaurantAvailableSchedule, (schedule) => schedule.restaurant, {
    cascade: true,
  })
  availableSchedule?: WrapperType<RestaurantAvailableSchedule>[];

  @Index()
  @Column({ name: 'schedule_active_at', nullable: true, type: 'timestamptz' })
  scheduleActiveAt?: Date | null;

  // location info
  @Column({ type: 'varchar', nullable: true })
  address: string | null;

  @Column({ type: 'varchar', nullable: true })
  ward: string | null;

  @Column({ type: 'varchar', nullable: true })
  district: string | null;

  @Column({ type: 'varchar', nullable: true })
  province: string | null;

  @Column({ type: 'varchar', nullable: true })
  phone: string | null;

  @Index()
  @Column({ type: 'decimal', precision: 10, scale: 8 })
  latitude: number;

  @Index()
  @Column({ type: 'decimal', precision: 11, scale: 8 })
  longitude: number;

  @Index('IDX_restaurants_location_geography', { spatial: true })
  @Column({
    type: 'geography',
    spatialFeatureType: 'Point',
    srid: 4326, // WGS84
  })
  location: { type: 'Point'; coordinates: number[] } | null;

  @ManyToOne(() => Brand, (brand) => brand.restaurants)
  @JoinColumn({ name: 'brand_id' })
  brand: WrapperType<Brand>;

  @ManyToMany(() => RestaurantTag, (restaurantTag) => restaurantTag.restaurants)
  @JoinTable({
    name: 'mapping_restaurant_tags_restaurants',
    joinColumn: {
      name: 'restaurant_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'restaurant_tag_id',
      referencedColumnName: 'id',
    },
  })
  tags: WrapperType<RestaurantTag>[];

  @OneToMany(() => Menu, (menu) => menu.restaurant)
  menus?: WrapperType<Menu>[];

  @OneToMany(() => Geofencing, (geofencing) => geofencing.restaurant)
  geofencing?: WrapperType<Geofencing>[];

  @OneToMany(() => UserFavouriteRestaurant, (userFavouriteRestaurant) => userFavouriteRestaurant.restaurant)
  favourites?: WrapperType<UserFavouriteRestaurant>[];

  distance?: number;
  isFavourite?: boolean;
  menu?: WrapperType<Menu>;
  popularItems?: WrapperType<MenuItem>[];
}
